<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج الفاتورة المضغوط</title>
    <link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="css/styleFeilds.css" rel="stylesheet" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            padding: 15px 20px;
            text-align: center;
        }
        
        .demo-content {
            padding: 20px;
        }
        
        /* محاكاة عناصر MudBlazor */
        .mud-paper {
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            margin-bottom: 8px;
        }
        
        .mud-input {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .mud-button {
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s;
        }
        
        .mud-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .grid-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .section-demo {
            padding: 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .field-demo {
            margin-bottom: 8px;
        }
        
        .field-demo label {
            display: block;
            margin-bottom: 2px;
            color: #666;
        }
        
        .field-demo input, .field-demo select {
            width: 100%;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.7rem;
            height: 30px;
        }
        
        .button-demo {
            padding: 2px 8px;
            font-size: 0.65rem;
            height: 26px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
        }
        
        .table-demo {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.65rem;
        }
        
        .table-demo th, .table-demo td {
            padding: 2px 4px;
            border: 1px solid #ddd;
            text-align: right;
        }
        
        .table-demo th {
            background: rgba(0,0,0,0.03);
            font-weight: 600;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #ffebee;
            border: 2px solid #f44336;
        }
        
        .after {
            background: #e8f5e8;
            border: 2px solid #4caf50;
        }
        
        .size-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h2>اختبار نموذج الفاتورة المضغوط</h2>
            <p>مقارنة بين الحجم الأصلي والحجم المضغوط</p>
        </div>
        
        <div class="demo-content compact-invoice-form">
            <div class="size-info">
                <strong>التحسينات المطبقة:</strong>
                <ul>
                    <li>تقليل حجم الخط من 0.8rem إلى 0.65rem</li>
                    <li>تقليل ارتفاع الحقول من 35px إلى 30px</li>
                    <li>تقليل الحشو من 8px إلى 4px</li>
                    <li>تقليل المسافات بين العناصر من 16px إلى 8px</li>
                    <li>تقليل ارتفاع الأزرار من 32px إلى 26px</li>
                </ul>
            </div>
            
            <div class="grid-demo">
                <!-- قسم معلومات الفاتورة -->
                <div class="section-demo">
                    <h4 style="font-size: 0.8rem; margin-bottom: 4px; color: #1976d2;">معلومات الفاتورة</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4px;">
                        <div class="field-demo">
                            <label>التاريخ</label>
                            <input type="date" value="2025-01-24" class="compact-field">
                        </div>
                        <div class="field-demo">
                            <label>رقم الفاتورة</label>
                            <input type="number" value="1001" class="compact-field">
                        </div>
                    </div>
                </div>
                
                <!-- قسم المخزن والمورد -->
                <div class="section-demo">
                    <h4 style="font-size: 0.8rem; margin-bottom: 4px; color: #1976d2;">المخزن والمورد</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4px;">
                        <div class="field-demo">
                            <label>المخازن</label>
                            <select class="compact-field">
                                <option>المخزن الرئيسي</option>
                                <option>مخزن فرعي</option>
                            </select>
                        </div>
                        <div class="field-demo">
                            <label>المورد</label>
                            <select class="compact-field">
                                <option>مورد تجريبي</option>
                                <option>مورد آخر</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قسم إضافة الأصناف -->
            <div class="section-demo">
                <h4 style="font-size: 0.8rem; margin-bottom: 4px; color: #1976d2;">إضافة صنف</h4>
                <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr; gap: 4px; align-items: end;">
                    <div class="field-demo">
                        <label>الصنف</label>
                        <input type="text" placeholder="ابدأ الكتابة للبحث الفوري" class="compact-field">
                    </div>
                    <div class="field-demo">
                        <label>الوحدة</label>
                        <select class="compact-field">
                            <option>قطعة</option>
                            <option>كيلو</option>
                        </select>
                    </div>
                    <div class="field-demo">
                        <label>تاريخ الانتهاء</label>
                        <input type="date" class="compact-field">
                    </div>
                    <div class="field-demo">
                        <label>الكمية</label>
                        <input type="number" value="0" class="compact-field">
                    </div>
                    <div class="field-demo">
                        <label>سعر الشراء</label>
                        <input type="number" value="0" class="compact-field">
                    </div>
                    <div class="field-demo">
                        <label>سعر البيع</label>
                        <input type="number" value="0" class="compact-field">
                    </div>
                </div>
                <button class="button-demo" style="margin-top: 8px; width: 100px;">إضافة الصنف</button>
            </div>
            
            <!-- قسم الإجماليات -->
            <div class="section-demo">
                <h4 style="font-size: 0.8rem; margin-bottom: 4px; color: #1976d2;">الإجماليات والدفع</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 4px;">
                    <div class="field-demo">
                        <label>الخزينة</label>
                        <select class="compact-field">
                            <option>الخزينة الرئيسية</option>
                        </select>
                    </div>
                    <div class="field-demo">
                        <label>الإجمالي</label>
                        <input type="number" value="0" readonly class="compact-field">
                    </div>
                    <div class="field-demo">
                        <label>المبلغ المدفوع</label>
                        <input type="number" value="0" class="compact-field">
                    </div>
                    <div class="field-demo">
                        <label>المبلغ المتبقي</label>
                        <input type="number" value="0" readonly class="compact-field">
                    </div>
                </div>
                
                <!-- خيارات الخصم -->
                <div style="margin-top: 8px;">
                    <label style="font-size: 0.65rem; margin-bottom: 4px; display: block;">نوع الخصم:</label>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <label style="font-size: 0.65rem; display: flex; align-items: center; gap: 4px;">
                            <input type="radio" name="discount" value="value" style="transform: scale(0.8);">
                            حسب القيمة
                        </label>
                        <label style="font-size: 0.65rem; display: flex; align-items: center; gap: 4px;">
                            <input type="radio" name="discount" value="percentage" style="transform: scale(0.8);">
                            بالنسبة المئوية
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- جدول الأصناف -->
            <div class="section-demo">
                <h4 style="font-size: 0.8rem; margin-bottom: 4px; color: #1976d2;">قائمة الأصناف</h4>
                <table class="table-demo">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الصنف</th>
                            <th>الوحدة</th>
                            <th>الصلاحية</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>صنف تجريبي</td>
                            <td>قطعة</td>
                            <td>2025-12-31</td>
                            <td>10</td>
                            <td>50.00</td>
                            <td>500.00</td>
                            <td>
                                <button class="button-demo" style="padding: 1px 4px; font-size: 0.6rem;">تعديل</button>
                                <button class="button-demo" style="padding: 1px 4px; font-size: 0.6rem; background: #f44336;">حذف</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- شريط الأزرار -->
            <div style="background: #f5f5f5; padding: 4px; border-radius: 4px; margin-top: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <button class="button-demo" style="background: #f44336;">رجوع</button>
                    <div style="display: flex; gap: 4px;">
                        <button class="button-demo">اضافة ايصال</button>
                        <button class="button-demo" style="background: #4caf50;">جديد (F8)</button>
                        <button class="button-demo">حفظ (F2)</button>
                        <button class="button-demo" style="background: #f44336;">حذف (Del)</button>
                        <button class="button-demo" style="background: #9e9e9e;">طباعة</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
