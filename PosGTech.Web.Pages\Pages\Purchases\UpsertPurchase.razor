﻿@page "/upsertPurchase/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]
<MudDialog Style="min-width: 100vw; height: 150vh;" Class="compact-invoice-form">
    <TitleContent>
        <div class="d-flex align-center gap-2">
            <MudIcon Icon="@Icons.Material.Filled.Receipt" Color="Color.Primary" Size="Size.Medium" />
            <div>
                <MudText Typo="Typo.h6" Color="Color.Primary">@(id == Guid.Empty ? "فاتورة شراء جديدة" : "تعديل فاتورة شراء")</MudText>
                <MudText Typo="Typo.caption" Color="Color.Secondary">@DateTime.Now.ToString("dd-MM-yyyy")</MudText>
            </div>
        </div>
    </TitleContent>

    <DialogContent>
        <EditForm Model="@purchase" id="my-form" @onkeydown="@keydownForm" @ref=Form Class="compact-invoice-form">
            <DataAnnotationsValidator />

            <!-- تصميم العمودين الرئيسي -->
            <div class="two-column-layout">

                <!-- العمود الأيمن: حقول النماذج وعناصر التحكم -->
                <div class="right-column">

                    <!-- قسم معلومات الفاتورة الأساسية -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">معلومات الفاتورة</MudText>
                        <MudGrid Spacing="1">
                            <MudItem xs="12" sm="6">
                                <MudDatePicker @bind-Date="_datePurchase"
                                               Label="التاريخ"
                                               Variant="Variant.Outlined"
                                               AdornmentIcon="@Icons.Material.Filled.Event"
                                               Adornment="Adornment.End"
                                               Margin="Margin.Dense"
                                               Class="compact-field" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <div class="d-flex align-center gap-1 invoice-navigation">
                                    <!-- زر التالي (→) -->
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowForward"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   Variant="Variant.Outlined"
                                                   OnClick="NavigateToNextInvoice"
                                                   Title="الفاتورة التالية (→)"
                                                   Class="navigation-btn flex-shrink-0 compact-btn"
                                                   Disabled="@(_isLoading || !PurchasesNum.Any() || (id != Guid.Empty && PurchasesNum.FindIndex(x => x.Id == id) >= PurchasesNum.Count - 1))" />

                                    <!-- حقل رقم الفاتورة -->
                                    <MudTextField T="int" Value="purchase.InvoiceNo" @ref="InvoiceNo"
                                                  Label="رقم الفاتورة" OnKeyDown="KeyDownInvoice"
                                                  ValueChanged="@((int value) => { purchase.InvoiceNo = value; ValidateInvoiceNumber(); })"
                                                  Variant="Variant.Outlined"
                                                  AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                  Adornment="Adornment.End"
                                                  For="@(() => purchase.InvoiceNo)"
                                                  Class="invoice-field flex-grow-1 compact-field"
                                                  Margin="Margin.Dense" />

                                    <!-- زر السابق (←) -->
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   Variant="Variant.Outlined"
                                                   OnClick="NavigateToPreviousInvoice"
                                                   Title="الفاتورة السابقة (←)"
                                                   Class="navigation-btn flex-shrink-0 compact-btn"
                                                   Disabled="@(_isLoading || !PurchasesNum.Any() || (id != Guid.Empty && PurchasesNum.FindIndex(x => x.Id == id) <= 0))" />
                                </div>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>

                    <!-- قسم المخزن والمورد -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">المخزن والمورد</MudText>
                        <MudGrid Spacing="1">
                            <MudItem xs="12" sm="6">
                                <MudSelect T="Guid?" @bind-Value="purchase.StoreId" Label="المخازن"
                                           Variant="Variant.Outlined"
                                           Adornment="Adornment.End"
                                           Disabled="purchase.Id!=Guid.Empty" AdornmentIcon="@Icons.Material.Filled.Store" Required="true"
                                           RequiredError="يجب اختيار المخزن" For="@(() =>purchase.StoreId)"
                                           Margin="Margin.Dense"
                                           Class="compact-field">
                                    <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                                    @foreach (var store in stores)
                                    {
                                        <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <div class="d-flex gap-1">
                                    <MudComboBox @bind-Value="purchase.Client"
                                                 Label="المورد" Editable="true"
                                                 SearchFunc="@SearchClientFunc"
                                                 ToStringFunc="@(e => e?.Name ?? string.Empty)"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.Person"
                                                 Adornment="Adornment.End" For="@(() => purchase.Client)"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field">
                                        @foreach (var clientItem in clients)
                                        {
                                            <MudComboBoxItem Value="@clientItem" Text="@clientItem.Name">@clientItem.Name</MudComboBoxItem>
                                        }
                                    </MudComboBox>

                                    <MudFab Style="height:26px;width:26px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.Person3"
                                            OnClick="AddNewClient" Color="Color.Info"
                                            Size="Size.Small"
                                            Class="compact-fab" />
                                </div>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>
                    <!-- قسم إضافة الأصناف -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">إضافة صنف</MudText>

                        <!-- البحث عن الصنف -->
                        <MudGrid Spacing="1">
                            <MudItem xs="12">
                                <div class="d-flex gap-1">
                                    <MudAutocomplete T="ItemCMDTO" @ref="ItemForAdd"
                                                     Value="selectedPurchaseItem.Item"
                                                     ValueChanged="OnItemSelected"
                                                     Text="@_searchText"
                                                     TextChanged="OnSearchTextChanged"
                                                     Label="الصنف"
                                                     SearchFunc="@SearchItemsWithPagination"
                                                     ToStringFunc="@(e => e?.Name ?? string.Empty)"
                                                     ResetValueOnEmptyText="false"
                                                     CoerceText="false"
                                                     CoerceValue="false"
                                                     OnKeyDown="SelectItem"
                                                     Variant="Variant.Outlined"
                                                     AdornmentIcon="@(_isLoadingItems ? Icons.Material.Filled.Autorenew : _isQuickSearching ? Icons.Material.Filled.Search : Icons.Material.Filled.Inventory)"
                                                     Adornment="Adornment.End"
                                                     DebounceInterval="200"
                                                     MaxItems="50"
                                                     MinCharacters="1"
                                                     ShowProgressIndicator="@(_isLoadingItems || _isQuickSearching)"
                                                     ProgressIndicatorColor="Color.Primary"
                                                     SelectOnClick="false"
                                                     HelperText="@(_isLoadingItems ? "جاري التحميل..." : _isQuickSearching ? "جاري البحث السريع..." : "ابدأ الكتابة للبحث الفوري")"
                                                     For="@(() =>selectedPurchaseItem.Item)"
                                                     Margin="Margin.Dense"
                                                     Class="compact-field">
                                        <ItemTemplate Context="item">
                                            <div class="d-flex align-center gap-2" style="padding: 6px; transition: all 0.2s ease; border-radius: 4px;">
                                                <MudIcon Icon="Icons.Material.Filled.Inventory"
                                                         Color="Color.Primary"
                                                         Size="Size.Small" />
                                                <div class="flex-grow-1">
                                                    <MudText Typo="Typo.body2" Style="font-weight: 500; color: #1976d2;">
                                                        @item.Name
                                                    </MudText>
                                                    @if (item.ItemNums?.Any() == true)
                                                    {
                                                        <div class="d-flex align-center gap-1" style="margin-top: 2px;">
                                                            <MudIcon Icon="Icons.Material.Filled.QrCode" Size="Size.Small" Color="Color.Secondary" />
                                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                                @string.Join(", ", item.ItemNums.Select(x => x.Barcode))
                                                            </MudText>
                                                        </div>
                                                    }
                                                </div>
                                                <MudChip T="string"
                                                         Size="Size.Small"
                                                         Color="Color.Success"
                                                         Variant="Variant.Text"
                                                         Style="font-size: 0.7em;">
                                                    متاح
                                                </MudChip>
                                            </div>
                                        </ItemTemplate>
                                        <MoreItemsTemplate>
                                            @if (_hasMoreItems && !_isLoadingItems)
                                            {
                                                <MudMenuItem OnClick="LoadMoreItems" Class="load-more-item">
                                                    <div class="d-flex align-center gap-2">
                                                        <MudIcon Icon="@Icons.Material.Filled.ExpandMore" Size="Size.Small" Color="Color.Primary" />
                                                        <MudText Typo="Typo.body2" Color="Color.Primary">
                                                            تحميل المزيد... (@(_totalItemsCount - _displayedItems.Count) متبقي)
                                                        </MudText>
                                                    </div>
                                                </MudMenuItem>
                                            }
                                            else if (_isLoadingItems)
                                            {
                                                <MudMenuItem Disabled="true" Class="loading-item">
                                                    <div class="d-flex align-center gap-2">
                                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Color="Color.Primary" />
                                                        <MudText Typo="Typo.body2" Color="Color.Secondary">جاري التحميل...</MudText>
                                                    </div>
                                                </MudMenuItem>
                                            }
                                            else if (_displayedItems.Count == 0 && !_isLoadingItems)
                                            {
                                                <MudMenuItem Disabled="true">
                                                    <div class="d-flex align-center gap-2">
                                                        <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Small" Color="Color.Secondary" />
                                                        <MudText Typo="Typo.body2" Color="Color.Secondary">لا توجد نتائج</MudText>
                                                    </div>
                                                </MudMenuItem>
                                            }
                                        </MoreItemsTemplate>
                                    </MudAutocomplete>

                                    <MudFab Style="height:26px;width:26px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.AddShoppingCart"
                                            OnClick="AddNewItem" Color="Color.Info"
                                            Size="Size.Small"
                                            Class="compact-fab" />
                                </div>
                            </MudItem>

                            <!-- تفاصيل الصنف -->
                            <MudItem xs="6" sm="4">
                                <MudSelect T="ItemUnitDTO" @ref="ItemUnit"
                                           Value="selectedPurchaseItem.ItemUnit"
                                           ValueChanged="ChangeItemUnit"
                                           Label="الوحدة"
                                           Variant="Variant.Outlined"
                                           AdornmentIcon="@Icons.Material.Filled.Scale"
                                           Adornment="Adornment.End"
                                           For="@(() =>selectedPurchaseItem.ItemUnit)"
                                           Margin="Margin.Dense"
                                           Class="compact-field">
                                    <MudSelectItem T="ItemUnitDTO" Value="null">اختر وحدة</MudSelectItem>
                                    @if (selectedPurchaseItem.Item != null)
                                    {
                                        @foreach (var unit in items.First(x => x.Id == selectedPurchaseItem.Item.Id).ItemUnits)
                                        {
                                            <MudSelectItem T="ItemUnitDTO" Value="@unit">@unit.Unit.Name</MudSelectItem>
                                        }
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudDatePicker PickerVariant="PickerVariant.Dialog" Label="تاريخ الصلاحية"
                                               Disabled="@(selectedPurchaseItem.Item==null?true:!items.First(x => x.Id == selectedPurchaseItem.Item.Id).IsHaveExp)"
                                               @bind-Date=_dateExp Mask="@(new DateMask("0000-00-00"))"
                                               DateFormat="yyyy-MM-dd" For="@(() =>_dateExp)"
                                               Variant="Variant.Outlined"
                                               AdornmentIcon="@Icons.Material.Filled.Event"
                                               Margin="Margin.Dense"
                                               Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudNumericField @bind-Value="selectedPurchaseItem.Quantity"
                                                 Label="الكمية"
                                                 Variant="Variant.Outlined"
                                                 Min="0"
                                                 AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudNumericField @bind-Value="selectedPurchaseItem.Price" Min="0"
                                                 Label="سعر شراء"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudNumericField T="decimal" @bind-Value="SalePriceChange" Min="0"
                                                 Label="سعر البيع"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.PriceCheck"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="12" sm="4" Class="d-flex align-center">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           OnClick="AddItemPurchase"
                                           StartIcon="@Icons.Material.Filled.Add"
                                           FullWidth="true"
                                           Size="Size.Small"
                                           Class="compact-btn"
                                           Style="height: 28px; font-size: 0.7rem;">
                                    إضافة الصنف
                                </MudButton>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>
                    <!-- قسم الإجماليات وطرق الدفع -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">الإجماليات والدفع</MudText>
                        <MudGrid Spacing="1">
                            @if (id == Guid.Empty)
                            {
                                <MudItem xs="12" sm="6">
                                    <MudSelect T="Guid?"
                                               @bind-Value="purchase.TreasuryId"
                                               Label="الخزينة" For="@(() =>purchase.TreasuryId)"
                                               Variant="Variant.Outlined"
                                               Margin="Margin.Dense"
                                               Class="compact-field">
                                        <MudSelectItem T="Guid?" Value="null">اختيار الخزينة</MudSelectItem>
                                        @foreach (var userTreasury in userTreasuries)
                                        {
                                            <MudSelectItem T="Guid?" Value="userTreasury.Id">@userTreasury.Treasury.Name</MudSelectItem>
                                        }
                                    </MudSelect>
                                </MudItem>
                            }

                            <MudItem xs="6" sm="3">
                                <MudTextField @bind-Value="purchase.Total"
                                              Label="الإجمالي"
                                              Variant="Variant.Outlined"
                                              ReadOnly="true"
                                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudTextField T="decimal" Value="purchase.DiscountValue" ValueChanged="ChangeDiscountValue"
                                              Label="التخفيض"
                                              Variant="Variant.Outlined"
                                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <div class="d-flex align-center small-radio-group">
                                    <MudRadioGroup T="bool" Value="purchase.IsDiscountValue" ValueChanged="ChangeIsDiscount" Class="d-flex gap-1">
                                        <MudRadio Value="true" Color="Color.Success">
                                            <div class="d-flex gap-1 align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.EventAvailable" Size="Size.Small" />
                                                <span style="font-size: 0.65rem;">بالقيمة</span>
                                            </div>
                                        </MudRadio>
                                        <MudRadio Value="false" Color="Color.Error">
                                            <div class="d-flex gap-1 align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Small" />
                                                <span style="font-size: 0.65rem;">بالنسبة</span>
                                            </div>
                                        </MudRadio>
                                    </MudRadioGroup>
                                </div>
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudTextField @bind-Value="purchase.FinalTotal"
                                              Label="الإجمالي بعد التخفيض" ReadOnly="true"
                                              Variant="Variant.Outlined"
                                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudNumericField @bind-Value="purchase.Paid"
                                                 Label="المدفوع" Disabled="@(id != Guid.Empty)"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.Payment"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudTextField Value="@(purchase.FinalTotal - purchase.Paid)"
                                              Label="المتبقي"
                                              Variant="Variant.Outlined"
                                              ReadOnly="true"
                                              AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>
                        </MudGrid>
                    </MudPaper>
                </div>

                <!-- العمود الأيسر: قائمة الأصناف -->
                <div class="left-column">
                    <MudPaper Elevation="0" Class="pa-2 items-list-container transparent-background">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">قائمة الأصناف</MudText>

                        <div class="items-table-container">
                            <MudTable Items="@purchase.PurchaseItemDTOs"
                                      Hover="true"
                                      Striped="true"
                                      Bordered="true"
                                      Height="calc(100vh - 300px)"
                                      FixedHeader="true"
                                      Class="compact-table">
                                <HeaderContent>
                                    <MudTh Style="width: 5%;">#</MudTh>
                                    <MudTh Style="width: 25%;">الصنف</MudTh>
                                    <MudTh Style="width: 10%;">الوحدة</MudTh>
                                    <MudTh Style="width: 10%;">الصلاحية</MudTh>
                                    <MudTh Style="width: 10%;">الكمية</MudTh>
                                    <MudTh Style="width: 10%;">السعر</MudTh>
                                    <MudTh Style="width: 10%;">بعد التخفيض</MudTh>
                                    <MudTh Style="width: 10%;">الإجمالي</MudTh>
                                    <MudTh Style="width: 10%;">إجراءات</MudTh>
                                </HeaderContent>
                                <RowTemplate Context="item">
                                    <MudTd DataLabel="#" Style="font-size: 0.8rem;">@(purchase.PurchaseItemDTOs.IndexOf(item) + 1)</MudTd>
                                    <MudTd DataLabel="الصنف" Style="font-size: 0.8rem;">@item.Item?.Name</MudTd>
                                    <MudTd DataLabel="الوحدة" Style="font-size: 0.8rem;">@item.ItemUnit?.Unit?.Name</MudTd>
                                    <MudTd DataLabel="الصلاحية" Style="font-size: 0.8rem;">@(item.Exp is null ? "لا يوجد" : item.Exp.Value.ToShortDateString())</MudTd>
                                    <MudTd DataLabel="الكمية" Style="font-size: 0.8rem;">@item.Quantity.ToString("N2")</MudTd>
                                    <MudTd DataLabel="السعر" Style="font-size: 0.8rem;">@item.Price.ToString("N2")</MudTd>
                                    <MudTd DataLabel="بعد التخفيض" Style="font-size: 0.8rem;">@item.PriceAfterDiscount.ToString("N2")</MudTd>
                                    <MudTd DataLabel="الإجمالي" Style="font-size: 0.8rem; font-weight: 600;">@((item.Quantity * item.PriceAfterDiscount).ToString("N2"))</MudTd>
                                    <MudTd DataLabel="إجراءات">
                                        <div class="d-flex gap-1">
                                            <MudIconButton Size="Size.Small"
                                                           Color="Color.Info"
                                                           Icon="@Icons.Material.Filled.Edit"
                                                           OnClick="@(() => EditItemPurchase(item))"
                                                           Title="تعديل" />

                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                           Color="Color.Error"
                                                           Size="Size.Small"
                                                           OnClick="@(() => DeleteItemPurchase(item))"
                                                           Title="حذف" />
                                        </div>
                                    </MudTd>
                                </RowTemplate>
                            </MudTable>
                        </div>
                    </MudPaper>
                </div>
            </div>

            <!-- شريط الأزرار السفلي -->
            <div class="action-buttons-bar">
                <MudPaper Elevation="2" Class="pa-1">
                    <div class="d-flex align-center gap-1 justify-space-between">
                        <div class="d-flex gap-1">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Error"
                                       OnClick="Back"
                                       StartIcon="@Icons.Material.Filled.ArrowBack"
                                       Disabled="@_isLoading"
                                       Size="Size.Small"
                                       Class="compact-btn"
                                       Style="height: 26px; font-size: 0.65rem;">
                                رجوع
                            </MudButton>
                        </div>

                        <div class="d-flex gap-1">
                            @if (id != Guid.Empty)
                            {
                                <MudButton Variant="Variant.Filled" Color="Color.Primary"
                                           OnClick="@(()=>AddReceipt(FinancialId.Purchase))"
                                           EndIcon="@(_isReceiptLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Receipt)"
                                           Disabled="@_isLoading"
                                           Size="Size.Small"
                                           Class="compact-btn"
                                           Style="height: 26px; font-size: 0.65rem;">
                                    @if (_isReceiptLoading)
                                    {
                                        <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                        <span style="margin-right: 4px;">جاري الإضافة...</span>
                                    }
                                    else
                                    {
                                        <span>اضافة ايصال</span>
                                    }
                                </MudButton>

                                <!-- زر معاينة الفاتورة -->
                                <MudButton Variant="Variant.Text"
                                           Color="Color.Info"
                                           OnClick="@PreviewPurchase"
                                           Disabled="@_isPrinting"
                                           StartIcon="@Icons.Material.Filled.Preview"
                                           Size="Size.Small"
                                           Class="compact-btn"
                                           Style="height: 26px; font-size: 0.65rem;">
                                    معاينة
                                </MudButton>
                            }

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Success"
                                       OnClick="NewPurchase"
                                       StartIcon="@(_isNewLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Add)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small"
                                       Class="compact-btn"
                                       Style="height: 26px; font-size: 0.65rem;">
                                @if (_isNewLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 4px;">جاري الإنشاء...</span>
                                }
                                else
                                {
                                    <span>جديد (F8)</span>
                                }
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="Upsert"
                                       StartIcon="@(_isSaveLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Save)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small"
                                       Class="compact-btn"
                                       Style="height: 26px; font-size: 0.65rem;">
                                @if (_isSaveLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 4px;">جاري الحفظ...</span>
                                }
                                else
                                {
                                    <span>حفظ (F2)</span>
                                }
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Error"
                                       OnClick="@(() => Delete(purchase))"
                                       StartIcon="@(_isDeleteLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Delete)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small"
                                       Class="compact-btn"
                                       Style="height: 26px; font-size: 0.65rem;">
                                @if (_isDeleteLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 4px;">جاري الحذف...</span>
                                }
                                else
                                {
                                    <span>حذف (Del)</span>
                                }
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Surface" OnClick="@Print"
                                       StartIcon="@(_isPrintLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Print)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small"
                                       Class="compact-btn"
                                       Style="height: 26px; font-size: 0.65rem;">
                                @if (_isPrintLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 4px;">جاري الطباعة...</span>
                                }
                                else
                                {
                                    <span>طباعة</span>
                                }
                            </MudButton>
                        </div>
                    </div>
                </MudPaper>
            </div>

            <!-- رسالة التأكيد -->
            <MudMessageBox @ref="mbox"
                           CancelText="إلغاء"
                           Class="rounded-lg">
                <MessageContent>
                    <div class="d-flex flex-column gap-4">
                        <MudIcon Icon="@(_isDeleteMessage? Icons.Material.Filled.Delete:Icons.Material.Filled.Warning)"
                                 Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                                 Size="Size.Large"
                                 Class="mx-auto" />
                        <MudText Align="Align.Center">
                            @_message
                        </MudText>
                    </div>
                </MessageContent>
                <YesButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                               StartIcon="@(_isDeleteMessage? Icons.Material.Filled.DeleteForever:Icons.Material.Filled.Info)"
                               Size="Size.Large">
                        تأكيد
                    </MudButton>
                </YesButton>
                <NoButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(Color.Default)"
                               StartIcon="@(Icons.Material.Filled.Cancel)"
                               Size="Size.Large">
                        إلغاء
                    </MudButton>
                </NoButton>
            </MudMessageBox>
        </EditForm>
    </DialogContent>
</MudDialog>



<style>
    /* التصميم ذو العمودين الرئيسي - مضغوط */
    .compact-invoice-form .two-column-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px !important;
        height: calc(100vh - 180px);
        overflow: hidden;
        padding: 4px !important;
        margin-bottom: 45px !important;
    }

    /* العمود الأيمن - حقول النماذج - مضغوط */
    .compact-invoice-form .right-column {
        display: flex;
        flex-direction: column;
        gap: 4px !important;
        overflow-y: auto;
        padding-right: 4px !important;
        max-height: 100%;
    }

    /* العمود الأيسر - قائمة الأصناف - مضغوط */
    .compact-invoice-form .left-column {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        max-height: 100%;
    }

    /* حاوية قائمة الأصناف */
    .items-list-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    /* حاوية الجدول */
    .items-table-container {
        flex: 1;
        overflow: hidden;
        margin-top: 8px;
    }

    /* جدول مضغوط محسن */
    .compact-invoice-form .compact-table {
        font-size: 0.65rem !important;
    }

    .compact-invoice-form .compact-table .mud-table-cell {
        padding: 2px 4px !important;
        font-size: 0.65rem !important;
        line-height: 1.2 !important;
    }

    .compact-invoice-form .compact-table .mud-table-head .mud-table-cell {
        padding: 3px 4px !important;
        font-size: 0.65rem !important;
        font-weight: 600 !important;
        background-color: rgba(0, 0, 0, 0.03) !important;
    }

    /* شريط الأزرار السفلي - مضغوط */
    .compact-invoice-form .action-buttons-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: white;
        border-top: 1px solid #e0e0e0;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        height: 40px !important;
    }

    /* تحسينات للتخطيط العربي RTL */
    .invoice-navigation {
        direction: rtl;
    }

    .invoice-navigation .d-flex {
        flex-direction: row-reverse;
    }

    /* تحسين أزرار التنقل */
    .navigation-btn {
        min-width: 32px;
        height: 32px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .navigation-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .navigation-btn:active {
        transform: scale(0.95);
    }

    /* تحسين حقل رقم الفاتورة */
    .invoice-field {
        margin: 0 4px;
    }

    /* تصميم متجاوب */
    @@media (max-width: 1200px) {
        .two-column-layout {
            grid-template-columns: 1fr;
            grid-template-rows: auto 1fr;
            height: auto;
            max-height: calc(100vh - 200px);
        }

        .right-column {
            max-height: 50vh;
            overflow-y: auto;
        }

        .left-column {
            max-height: 50vh;
        }
    }

    @@media (max-width: 768px) {
        .two-column-layout {
            gap: 8px;
            padding: 4px;
        }

        .action-buttons-bar .d-flex {
            flex-wrap: wrap;
            gap: 4px !important;
        }

        .action-buttons-bar .mud-button {
            font-size: 0.7rem !important;
            padding: 4px 8px !important;
        }
    }

    /* تحسينات للبحث المترقم */
    .load-more-item {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.05) !important;
        border-top: 1px solid rgba(var(--mud-palette-primary-rgb), 0.12);
        transition: background-color 0.2s ease;
    }

    .load-more-item:hover {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.08) !important;
    }

    .loading-item {
        background-color: rgba(var(--mud-palette-surface-rgb), 0.5) !important;
        border-top: 1px solid rgba(var(--mud-palette-divider-rgb), 0.12);
    }

    /* تحسين عرض عناصر البحث */
    .mud-autocomplete .mud-list-item {
        padding: 8px 12px;
        border-bottom: 1px solid rgba(var(--mud-palette-divider-rgb), 0.08);
    }

    .mud-autocomplete .mud-list-item:hover {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.04);
    }

    .mud-autocomplete .mud-list-item:last-child {
        border-bottom: none;
    }

    /* تحسينات البحث السريع المتقدمة */
    .mud-autocomplete .mud-input-control:focus-within {
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        border-color: #1976d2;
        transition: all 0.2s ease;
    }

    .mud-autocomplete .mud-list-item.mud-selected-item {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.08);
        border-left: 3px solid var(--mud-palette-primary);
        transform: translateX(2px);
    }

    /* مؤشر التحميل المحسن */
    .mud-progress-circular {
        animation: pulse 1.5s ease-in-out infinite;
    }

    @@keyframes pulse {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.05); }
        100% { opacity: 1; transform: scale(1); }
    }

    /* تحسين مظهر الرقائق */
    .mud-chip {
        transition: all 0.2s ease;
        border-radius: 12px;
    }

    .mud-chip:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* تحسين مظهر الأيقونات */
    .mud-icon-root {
        transition: all 0.2s ease;
    }

    .mud-icon-root:hover {
        transform: scale(1.1);
    }

    /* تحسين مظهر النص المساعد */
    .mud-input-helper-text {
        font-size: 0.75rem;
        color: var(--mud-palette-text-secondary);
        transition: color 0.2s ease;
    }

    /* تحسين التركيز */
    .mud-input-control:focus-within .mud-input-helper-text {
        color: var(--mud-palette-primary);
    }

    /* تحسينات إضافية للتصميم المضغوط */
    .small-radio-group .mud-radio {
        margin: 0 4px;
    }

    .small-radio-group .mud-radio .mud-radio-content {
        font-size: 0.8rem;
    }

    /* تحسين المساحات في الحقول المضغوطة */
    .mud-input-control.mud-input-control-margin-dense {
        margin-top: 4px !important;
        margin-bottom: 4px !important;
    }

    /* تحسين عرض الجدول في الشاشات الصغيرة */
    @@media (max-width: 768px) {
        .compact-table .mud-table-cell {
            padding: 2px 4px !important;
            font-size: 0.7rem !important;
        }

        .compact-table .mud-table-head .mud-table-cell {
            padding: 4px 4px !important;
            font-size: 0.7rem !important;
        }
    }

    /* تحسين التمرير في العمود الأيمن */
    .right-column::-webkit-scrollbar {
        width: 6px;
    }

    .right-column::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .right-column::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .right-column::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* إخفاء شريط الأزرار من المساحة المحجوزة للمحتوى */
    .two-column-layout {
        margin-bottom: 60px;
    }

    /* تحسين عرض عناصر البحث للتصميم المضغوط */
    .mud-autocomplete .mud-list-item {
        padding: 6px 8px;
        border-bottom: 1px solid rgba(var(--mud-palette-divider-rgb), 0.08);
    }

    /* إزالة الخلفية البيضاء من قائمة الأصناف */
    .transparent-background {
        background-color: transparent !important;
        background: transparent !important;
    }

    .transparent-background .mud-paper {
        background-color: transparent !important;
        background: transparent !important;
    }

    /* إزالة الخلفية من الجدول أيضاً */
    .items-list-container .mud-table {
        background-color: transparent !important;
        background: transparent !important;
    }

    .items-list-container .mud-table-container {
        background-color: transparent !important;
        background: transparent !important;
    }

    /* إزالة الخلفية من رؤوس الجدول */
    .items-list-container .mud-table-head {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    /* تحسين مظهر الصفوف بدون خلفية بيضاء */
    .items-list-container .mud-table-row {
        background-color: transparent !important;
    }

    .items-list-container .mud-table-row:hover {
        background-color: rgba(0, 0, 0, 0.04) !important;
    }

    .items-list-container .mud-table-row:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02) !important;
    }
</style>

