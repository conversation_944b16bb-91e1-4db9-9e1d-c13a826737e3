﻿/* تصغير حقول النص العامة */
.small-TextField .mud-input {
    font-size: 0.7rem !important;
    height: 32px !important;
    padding: 4px 8px !important;
}

.small-TextField label {
    font-size: 0.65rem !important;
    transform: scale(0.9) !important;
}

.small-TextField .mud-input-adornment {
    font-size: 0.7rem !important;
}

/* تصغير عناصر العمود الأيمن فقط */
.compact-right-column .mud-input {
    font-size: 0.7rem !important;
    height: 30px !important;
    padding: 3px 6px !important;
}

.compact-right-column .mud-input-label {
    font-size: 0.65rem !important;
    transform: scale(0.85) !important;
}

.compact-right-column .mud-select .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

.compact-right-column .mud-autocomplete .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

.compact-right-column .mud-numeric-field .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

.compact-right-column .mud-date-picker .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

/*______________________________________________________________________*/

/* تصغير حجم الـ Radio Group */
.small-radio-group {
    padding: 2px !important;
    min-height: min-content !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.small-radio-group .mud-radio {
    transform: scale(0.8) !important;
    margin: 0 2px !important;
}

.small-radio-group .mud-radio span {
    font-size: 0.7rem !important;
    line-height: 1 !important;
}

.small-radio-group .d-flex {
    gap: 2px !important;
    align-items: center;
}

.small-radio-group .mud-paper {
    padding: 2px !important;
    display: inline-block;
    width: auto;
    height: auto;
}

/* الحفاظ على الحجم الأصلي للأزرار - إزالة التصغير */
/* تم إزالة تصغير الأزرار للحفاظ على الحجم الأصلي */

/* تصغير النصوص والعناوين في العمود الأيمن فقط */
.compact-right-column .mud-typography-h6,
.compact-right-column .mud-typography-subtitle2 {
    font-size: 0.8rem !important;
    margin-bottom: 4px !important;
    font-weight: 600 !important;
}

.compact-right-column .mud-typography-body2 {
    font-size: 0.65rem !important;
}

/* تصغير المساحات والحشو في العمود الأيمن فقط */
.compact-right-column .mud-paper {
    padding: 8px !important;
    margin-bottom: 6px !important;
}

.compact-right-column .mud-grid {
    margin: 0 !important;
}

.compact-right-column .mud-grid-item {
    padding: 2px !important;
}

/* الحفاظ على الحجم الأصلي لجدول الأصناف */
/* تم إزالة تصغير الجدول للحفاظ على الحجم الأصلي */

/* تصغير عناصر البحث والقوائم المنسدلة في العمود الأيمن فقط */
.compact-right-column .mud-autocomplete .mud-list-item {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
}

.compact-right-column .mud-select-item {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
}

.compact-right-column .mud-chip {
    font-size: 0.6rem !important;
    height: 18px !important;
    padding: 0 4px !important;
}

/* تصغير النص المساعد في العمود الأيمن فقط */
.compact-right-column .mud-input-helper-text {
    font-size: 0.6rem !important;
    margin-top: 1px !important;
}

/* تصغير الأيقونات في العمود الأيمن فقط */
.compact-right-column .mud-icon-root {
    font-size: 0.9rem !important;
}

.compact-right-column .mud-input-adornment .mud-icon-root {
    font-size: 0.8rem !important;
}
/*______________________________________________________________*/
/* تصغير ارتفاع الأزرار والخط */
.small-button {
    font-size: 0.7rem !important;
    height: 26px !important;
    padding: 2px 6px !important;
    min-width: auto !important;
}

.button-small {
    height: 26px !important;
    padding: 2px 6px !important;
    font-size: 0.65rem !important;
    font-weight: 600 !important;
}

/* الحفاظ على الحجم الأصلي لشريط الأزرار السفلي */
/* تم إزالة تصغير شريط الأزرار للحفاظ على الحجم الأصلي */

/* تصغير عناصر التنقل في العمود الأيمن فقط */
.compact-right-column .navigation-btn {
    min-width: 26px !important;
    height: 26px !important;
    padding: 2px !important;
}

.compact-right-column .invoice-field {
    margin: 0 2px !important;
}

/* تصغير عناصر النموذج الخاصة في العمود الأيمن فقط */
.compact-right-column .mud-date-picker .mud-input-adornment {
    padding: 0 4px !important;
}

.compact-right-column .mud-select .mud-input-adornment {
    padding: 0 4px !important;
}

.compact-right-column .mud-autocomplete .mud-input-adornment {
    padding: 0 4px !important;
}

/* تصغير المساحات في العمود الأيمن فقط */
.compact-right-column {
    gap: 4px !important;
    padding-right: 4px !important;
}

/* الحفاظ على الحجم الأصلي لقائمة الأصناف */
/* تم إزالة تصغير قائمة الأصناف للحفاظ على الحجم الأصلي */

/* تحسينات للشاشات الصغيرة - العمود الأيمن فقط */
@media (max-width: 768px) {
    .compact-right-column .mud-input {
        font-size: 0.65rem !important;
        height: 28px !important;
    }
}

/* كلاسات مساعدة للتصميم المضغوط */
.compact-field {
    font-size: 0.7rem !important;
}

.compact-field .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
    padding: 3px 6px !important;
}

.compact-field .mud-input-label {
    font-size: 0.65rem !important;
    transform: scale(0.85) !important;
}

.compact-btn {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
    min-height: 26px !important;
    padding: 2px !important;
}

.compact-fab {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
    min-height: 26px !important;
}

.compact-fab .mud-icon-root {
    font-size: 0.8rem !important;
}

/* تصغير عناصر الحوار */
.compact-invoice-form .mud-dialog {
    font-size: 0.8rem !important;
}

.compact-invoice-form .mud-dialog-title {
    font-size: 0.9rem !important;
    padding: 6px 12px !important;
}

.compact-invoice-form .mud-dialog-content {
    padding: 8px !important;
}

/* تصغير عناصر التحقق من الصحة */
.compact-invoice-form .validation-message {
    font-size: 0.6rem !important;
    margin-top: 1px !important;
}

/* تصغير عناصر القوائم المنسدلة */
.compact-invoice-form .mud-popover-content {
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-list-item {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
    min-height: 24px !important;
}

/* تصغير عناصر التاريخ */
.compact-invoice-form .mud-picker-calendar-header {
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-picker-calendar-day {
    font-size: 0.65rem !important;
    width: 24px !important;
    height: 24px !important;
}

/* تصغير عناصر الرسائل */
.compact-invoice-form .mud-snackbar {
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-alert {
    font-size: 0.7rem !important;
    padding: 4px 8px !important;
}

/*________________________________________________________________________________*/

/*upsertInventory*/



