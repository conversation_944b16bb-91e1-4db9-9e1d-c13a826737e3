﻿/* تصغير حقول النص العامة */
.small-TextField .mud-input {
    font-size: 0.7rem !important;
    height: 32px !important;
    padding: 4px 8px !important;
}

.small-TextField label {
    font-size: 0.65rem !important;
    transform: scale(0.9) !important;
}

.small-TextField .mud-input-adornment {
    font-size: 0.7rem !important;
}

/* تصغير عناصر نموذج الفاتورة */
.compact-invoice-form .mud-input {
    font-size: 0.7rem !important;
    height: 30px !important;
    padding: 3px 6px !important;
}

.compact-invoice-form .mud-input-label {
    font-size: 0.65rem !important;
    transform: scale(0.85) !important;
}

.compact-invoice-form .mud-select .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-autocomplete .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-numeric-field .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-date-picker .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
}

/*______________________________________________________________________*/

/* تصغير حجم الـ Radio Group */
.small-radio-group {
    padding: 2px !important;
    min-height: min-content !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.small-radio-group .mud-radio {
    transform: scale(0.8) !important;
    margin: 0 2px !important;
}

.small-radio-group .mud-radio span {
    font-size: 0.7rem !important;
    line-height: 1 !important;
}

.small-radio-group .d-flex {
    gap: 2px !important;
    align-items: center;
}

.small-radio-group .mud-paper {
    padding: 2px !important;
    display: inline-block;
    width: auto;
    height: auto;
}

/* تصغير أزرار نموذج الفاتورة */
.compact-invoice-form .mud-button {
    font-size: 0.7rem !important;
    height: 28px !important;
    padding: 2px 8px !important;
    min-width: auto !important;
}

.compact-invoice-form .mud-fab {
    width: 28px !important;
    height: 28px !important;
    min-height: 28px !important;
}

.compact-invoice-form .mud-icon-button {
    width: 26px !important;
    height: 26px !important;
    padding: 2px !important;
}

/* تصغير النصوص والعناوين */
.compact-invoice-form .mud-typography-h6 {
    font-size: 0.8rem !important;
    margin-bottom: 4px !important;
    font-weight: 600 !important;
}

.compact-invoice-form .mud-typography-body2 {
    font-size: 0.65rem !important;
}

/* تصغير المساحات والحشو */
.compact-invoice-form .mud-paper {
    padding: 8px !important;
    margin-bottom: 6px !important;
}

.compact-invoice-form .mud-grid {
    margin: 0 !important;
}

.compact-invoice-form .mud-grid-item {
    padding: 2px !important;
}

/* تصغير عناصر الجدول */
.compact-invoice-form .mud-table {
    font-size: 0.65rem !important;
}

.compact-invoice-form .mud-table-cell {
    padding: 2px 4px !important;
    font-size: 0.65rem !important;
    line-height: 1.2 !important;
}

.compact-invoice-form .mud-table-head .mud-table-cell {
    padding: 3px 4px !important;
    font-size: 0.65rem !important;
    font-weight: 600 !important;
}

/* تصغير عناصر البحث والقوائم المنسدلة */
.compact-invoice-form .mud-autocomplete .mud-list-item {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
}

.compact-invoice-form .mud-select-item {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
}

.compact-invoice-form .mud-chip {
    font-size: 0.6rem !important;
    height: 18px !important;
    padding: 0 4px !important;
}

/* تصغير النص المساعد */
.compact-invoice-form .mud-input-helper-text {
    font-size: 0.6rem !important;
    margin-top: 1px !important;
}

/* تصغير الأيقونات */
.compact-invoice-form .mud-icon-root {
    font-size: 0.9rem !important;
}

.compact-invoice-form .mud-input-adornment .mud-icon-root {
    font-size: 0.8rem !important;
}
/*______________________________________________________________*/
/* تصغير ارتفاع الأزرار والخط */
.small-button {
    font-size: 0.7rem !important;
    height: 26px !important;
    padding: 2px 6px !important;
    min-width: auto !important;
}

.button-small {
    height: 26px !important;
    padding: 2px 6px !important;
    font-size: 0.65rem !important;
    font-weight: 600 !important;
}

/* تصغير شريط الأزرار السفلي */
.compact-invoice-form .action-buttons-bar .mud-button {
    font-size: 0.65rem !important;
    height: 26px !important;
    padding: 2px 6px !important;
    margin: 1px !important;
}

.compact-invoice-form .action-buttons-bar .mud-paper {
    padding: 4px !important;
}

/* تصغير عناصر التنقل */
.compact-invoice-form .navigation-btn {
    min-width: 26px !important;
    height: 26px !important;
    padding: 2px !important;
}

.compact-invoice-form .invoice-field {
    margin: 0 2px !important;
}

/* تصغير مربعات الحوار */
.compact-invoice-form .mud-dialog-title {
    padding: 8px !important;
    font-size: 0.9rem !important;
}

.compact-invoice-form .mud-dialog-content {
    padding: 12px !important;
}

/* تصغير عناصر النموذج الخاصة */
.compact-invoice-form .mud-date-picker .mud-input-adornment {
    padding: 0 4px !important;
}

.compact-invoice-form .mud-select .mud-input-adornment {
    padding: 0 4px !important;
}

.compact-invoice-form .mud-autocomplete .mud-input-adornment {
    padding: 0 4px !important;
}

/* تصغير المساحات في التخطيط ذو العمودين */
.compact-invoice-form .two-column-layout {
    gap: 8px !important;
    padding: 4px !important;
    margin-bottom: 50px !important;
}

.compact-invoice-form .right-column {
    gap: 4px !important;
    padding-right: 4px !important;
}

/* تصغير عناصر قائمة الأصناف */
.compact-invoice-form .items-list-container {
    padding: 4px !important;
}

.compact-invoice-form .items-table-container {
    margin-top: 4px !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .compact-invoice-form .mud-button {
        font-size: 0.6rem !important;
        height: 24px !important;
        padding: 1px 4px !important;
    }

    .compact-invoice-form .mud-input {
        font-size: 0.65rem !important;
        height: 28px !important;
    }

    .compact-invoice-form .mud-table-cell {
        padding: 1px 2px !important;
        font-size: 0.6rem !important;
    }
}

/* كلاسات مساعدة للتصميم المضغوط */
.compact-field {
    font-size: 0.7rem !important;
}

.compact-field .mud-input {
    height: 30px !important;
    font-size: 0.7rem !important;
    padding: 3px 6px !important;
}

.compact-field .mud-input-label {
    font-size: 0.65rem !important;
    transform: scale(0.85) !important;
}

.compact-btn {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
    min-height: 26px !important;
    padding: 2px !important;
}

.compact-fab {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
    min-height: 26px !important;
}

.compact-fab .mud-icon-root {
    font-size: 0.8rem !important;
}

/* تصغير عناصر الحوار */
.compact-invoice-form .mud-dialog {
    font-size: 0.8rem !important;
}

.compact-invoice-form .mud-dialog-title {
    font-size: 0.9rem !important;
    padding: 6px 12px !important;
}

.compact-invoice-form .mud-dialog-content {
    padding: 8px !important;
}

/* تصغير عناصر التحقق من الصحة */
.compact-invoice-form .validation-message {
    font-size: 0.6rem !important;
    margin-top: 1px !important;
}

/* تصغير عناصر القوائم المنسدلة */
.compact-invoice-form .mud-popover-content {
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-list-item {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
    min-height: 24px !important;
}

/* تصغير عناصر التاريخ */
.compact-invoice-form .mud-picker-calendar-header {
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-picker-calendar-day {
    font-size: 0.65rem !important;
    width: 24px !important;
    height: 24px !important;
}

/* تصغير عناصر الرسائل */
.compact-invoice-form .mud-snackbar {
    font-size: 0.7rem !important;
}

.compact-invoice-form .mud-alert {
    font-size: 0.7rem !important;
    padding: 4px 8px !important;
}

/*________________________________________________________________________________*/

/*upsertInventory*/



